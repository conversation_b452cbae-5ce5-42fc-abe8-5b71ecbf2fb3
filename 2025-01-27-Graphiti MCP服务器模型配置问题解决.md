# 2025-01-27 - Graphiti MCP服务器模型配置问题解决

## 需求
使用graphiti-memory MCP插件查看记忆内容时遇到"Model does not exist"错误，需要解决配置问题。

## 环境信息
- **MCP配置路径**: `c:\Users\<USER>\AppData\Roaming\Trae\User\mcp.json`
- **Graphiti服务器路径**: `D:\Project\node\crawl_all\django-vue3-admin\graphiti\mcp_server`
- **Neo4j数据库**: `bolt://172.20.0.21:7687`
- **当前模型配置**: `deepseek-ai/DeepSeek-V3`
- **API端点**: `https://api.siliconflow.cn/v1`

## 问题分析

### 当前状态
1. ✅ 添加记忆成功 - 记忆已排队处理
2. ❌ 搜索功能失败 - 提示"Model does not exist"
3. ❌ 获取episodes为空 - 可能因为处理失败

### 可能原因
1. **API端点问题**: 当前使用`https://api.siliconflow.cn/v1`，但搜索结果显示可能需要使用不同的端点
2. **模型名称格式**: `deepseek-ai/DeepSeek-V3` 格式经验证是正确的 ✅
3. **API密钥权限**: 当前API密钥可能没有访问DeepSeek-V3模型的权限
4. **网络连接**: 可能存在网络连接或防火墙问题

### 搜索结果分析
- SiliconFlow确实支持DeepSeek-V3模型 <mcreference link="https://www.siliconflow.com/models/deepseek-ai-deepseek-v3" index="4">4</mcreference>
- 正确的API端点应该是`https://api.siliconflow.cn/v1` <mcreference link="https://www.zhihu.com/question/12128730270" index="3">3</mcreference>
- 模型名称格式`deepseek-ai/DeepSeek-V3`是正确的 <mcreference link="https://www.siliconflow.com/models/deepseek-ai-deepseek-v3" index="4">4</mcreference>

## 执行方案

### 阶段1: 验证模型可用性 ✅
- [x] 检查SiliconFlow支持的模型列表 - DeepSeek-V3确实支持
- [x] 验证API密钥权限 - API调用成功，返回正常响应
- [x] 测试不同的模型名称格式 - `deepseek-ai/DeepSeek-V3`格式正确

**测试结果**: API配置完全正确，问题不在SiliconFlow API层面

### 阶段2: 配置修复
- [x] **发现关键问题**: 模型名称配置不一致
  - 当前MCP配置: `deepseek-ai/DeepSeek-V3`
  - 部署文档建议: `deepseek-chat`
  - 可能原因: Graphiti内部使用的模型标识符与SiliconFlow API不同
- [x] 测试使用`deepseek-chat`作为模型名称 - ❌ 该模型不存在
- [x] 用户调整配置文件并重启MCP服务器
- [x] 验证功能 - ❌ 问题仍然存在

**重启后测试结果**:
- ❌ 搜索功能仍报"Model does not exist"错误
- ❌ 记忆处理仍然失败（episodes为空）
- ✅ 外部API测试正常（SiliconFlow API可正常调用）

### 阶段3: 配置文件检查
- [x] **发现关键问题**: `.env`配置文件缺少`NEO4J_PASSWORD`
  - 配置文件路径: `D:\Project\node\crawl_all\django-vue3-admin\graphiti\mcp_server\.env`
  - 当前配置: 包含NEO4J_URI、NEO4J_USER，但缺少NEO4J_PASSWORD
  - 模型配置: `MODEL_NAME=deepseek-ai/DeepSeek-V3` ✅ 正确
  - API配置: `OPENAI_BASE_URL=https://api.siliconflow.cn/v1` ✅ 正确
  - API密钥: `OPENAI_API_KEY=sk-yevr...` ✅ 存在
- [x] **添加Neo4j密码配置**: `NEO4J_PASSWORD=graphiti123`
- [x] **发现真正的问题根源**: Graphiti源码中的默认模型配置错误
  - 源码文件: `graphiti_mcp_server.py`
  - 问题代码: `DEFAULT_LLM_MODEL = 'gpt-4.1-mini'` ❌ 不存在的模型
  - 小模型: `SMALL_LLM_MODEL = 'gpt-4.1-nano'` ❌ 不存在的模型
  - **根本原因**: 当环境变量`MODEL_NAME`为空或未正确读取时，系统回退到不存在的默认模型

### 阶段4: 源码修复
- [x] **修复默认模型配置**:
  - 修改前: `DEFAULT_LLM_MODEL = 'gpt-4.1-mini'` ❌
  - 修改后: `DEFAULT_LLM_MODEL = 'deepseek-ai/DeepSeek-V3'` ✅
  - 修改前: `SMALL_LLM_MODEL = 'gpt-4.1-nano'` ❌
  - 修改后: `SMALL_LLM_MODEL = 'deepseek-ai/DeepSeek-V3'` ✅
- [x] **测试修复效果**: ❌ 问题仍然存在
  - 可能原因: MCP服务器需要重启才能使源码修改生效
  - 建议: 重启MCP服务器后再次测试

### 阶段5: 编码问题修复
- [x] **发现新问题**: `.env`文件UTF-8编码错误
  - 错误信息: `UnicodeDecodeError: 'utf-8' codec can't decode bytes`
  - 原因: 中文注释导致编码问题
- [x] **修复编码问题**: 重新创建干净的`.env`文件
  - 移除所有中文注释，改为英文注释
  - 使用UTF-8编码保存
  - 保留所有必要的配置项
- [x] **测试修复效果**: ❌ 问题仍然存在
  - MCP服务器能正常接收请求，但模型调用仍然失败
  - **结论**: 需要完全重启MCP服务器进程才能使所有修改生效

### 阶段6: 发现根本问题 ✅
- [x] **发现真正的根本问题**: `OPENAI_BASE_URL`环境变量未被传递给LLMConfig
  - 问题位置: `graphiti_mcp_server.py` 第340行
  - 问题代码: `LLMConfig(api_key=self.api_key, model=self.model, small_model=self.small_model)`
  - **缺失**: 没有传递`base_url=os.environ.get('OPENAI_BASE_URL')`参数
  - **后果**: OpenAI客户端使用默认的OpenAI API端点，而不是SiliconFlow端点
- [x] **修复源码**: 添加`base_url=os.environ.get('OPENAI_BASE_URL')`参数
  - 修改后: `LLMConfig(api_key=self.api_key, model=self.model, small_model=self.small_model, base_url=os.environ.get('OPENAI_BASE_URL'))`

**重要发现**: 
- ✅ 当前模型名称`deepseek-ai/DeepSeek-V3`是**正确的**
- ✅ API密钥和端点配置都是**正确的**
- ❌ **根本问题**: 源码中没有读取`OPENAI_BASE_URL`环境变量，导致请求发送到错误的API端点
- ✅ **解决方案**: 修复源码中的base_url参数传递

### 阶段7: 功能验证
- [x] 重启MCP服务器
- [x] 测试添加记忆 - ✅ 成功排队
- [x] 测试搜索记忆 - ❌ 仍报错"Model does not exist"
- [ ] 测试获取记忆列表

### 阶段8: Embedder配置修复 ✅
- [x] **发现新问题**: Embedder配置也存在同样的base_url缺失问题
  - 问题位置: `graphiti_mcp_server.py` 第450行
  - 问题代码: `OpenAIEmbedderConfig(api_key=self.api_key, embedding_model=self.model)`
  - **缺失**: 没有传递`base_url=os.environ.get('OPENAI_BASE_URL')`参数
- [x] **修复源码**: 添加`base_url=os.environ.get('OPENAI_BASE_URL')`参数
  - 修改后: `OpenAIEmbedderConfig(api_key=self.api_key, embedding_model=self.model, base_url=os.environ.get('OPENAI_BASE_URL'))`

### 阶段9: Embedder模型配置问题

**时间:** 2025-01-27 16:15

**问题发现:**
在重启MCP服务器后，搜索功能仍然报错"Model does not exist"。进一步分析发现：
1. `.env`文件中缺少`EMBEDDER_MODEL_NAME`环境变量配置
2. `DEFAULT_EMBEDDER_MODEL`默认值为`text-embedding-3-small`（OpenAI模型）
3. SiliconFlow平台不支持OpenAI的embedding模型

**解决方案:**
在`.env`文件中添加SiliconFlow支持的embedder模型：
```bash
EMBEDDER_MODEL_NAME=BAAI/bge-large-zh-v1.5
```

**修复状态:** ✅ 已完成

### 阶段10: 最终功能验证 ✅
- [x] 重启MCP服务器
- [x] 测试完整的记忆功能（添加和搜索）

**验证结果:**

1. **search_memory_nodes 功能测试**: ✅ 成功
   - 不再出现 "Model does not exist" 错误
   - 返回正常的搜索结果（空结果，符合预期）

2. **add_memory 功能测试**: ✅ 成功
   - 成功添加测试记忆
   - 返回: "Episode '测试记忆添加' queued for processing (position: 1)"

3. **配置修复验证**: ✅ 全部生效
   - LLM 配置的 `base_url` 修复生效
   - Embedder 配置的 `base_url` 修复生效
   - Embedder 模型配置修复生效

**总结**: "Model does not exist"错误已完全解决，但发现新问题：记忆处理队列功能异常。

## 新发现的问题

### 问题描述
- `add_memory` 功能返回成功排队消息
- 但记忆实际上没有被处理和存储
- `get_episodes` 始终返回空结果
- 怀疑是异步队列工作器未正常启动或运行

### 可能原因
1. 异步任务管理问题
2. MCP 服务器进程间通信问题
3. 队列工作器生命周期管理问题

### 状态
❌ **标记为失败** - 用户决定停止测试

## 最终总结

✅ **成功解决的问题：**
- "Model does not exist" 错误完全修复
- LLM 配置 `base_url` 传递问题
- Embedder 配置 `base_url` 传递问题  
- Embedder 模型兼容性问题

❌ **未解决的问题：**
- 记忆处理队列功能异常
- 异步任务管理存在缺陷
- 实际功能无法正常使用

📊 **项目状态：** 部分成功 - API 调用正常，但核心功能失效

## 备注
- 需要确认SiliconFlow平台上DeepSeek-V3的正确模型标识符
- 可能需要切换到其他可用模型进行测试